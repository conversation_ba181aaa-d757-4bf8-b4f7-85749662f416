/* Custom CSS for ShopEase E-commerce Website */

/* Root Variables */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Global Styles */
body {
    font-family: var(--font-family);
    line-height: 1.6;
}

/* Custom Logo Styles */
.custom-logo {
    transition: transform 0.3s ease;
}

.custom-logo:hover {
    transform: scale(1.1);
}

.brand-text {
    font-size: 1.5rem;
    letter-spacing: -0.5px;
}

.navbar-brand:hover .custom-logo {
    transform: scale(1.1);
}

/* Navigation Styles */
.navbar {
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link {
    font-weight: 500;
    margin: 0 0.5rem;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    min-height: 60vh;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.min-vh-50 {
    min-height: 50vh;
}

/* Category Cards */
.category-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.hover-shadow:hover {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Product Cards */
.product-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.product-card .card-img-top {
    transition: all 0.3s ease;
}

.product-card:hover .card-img-top {
    background-color: #e9ecef !important;
}

/* Rating Stars */
.rating {
    font-size: 0.9rem;
}

/* Deal Cards */
.deal-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.deal-card:hover {
    transform: scale(1.05);
}

/* Button Styles */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
}

/* Search Bar */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Footer */
footer {
    border-top: 1px solid #dee2e6;
}

footer .social-links a {
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

footer ul li {
    margin-bottom: 0.5rem;
}

footer ul li a:hover {
    color: var(--primary-color) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        min-height: 40vh;
    }

    .brand-text {
        font-size: 1.25rem;
    }

    .display-4 {
        font-size: 2rem;
    }

    .navbar-nav {
        text-align: center;
    }

    .navbar-nav .nav-link {
        margin: 0.25rem 0;
    }
}

/* Animation Classes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Utility Classes */
.text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.border-radius-lg {
    border-radius: 15px;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--dark-color);
}